# 南京2024年空气质量数据爬取与分析项目

## 项目简介

本项目旨在爬取南京市2024年一整年的真实空气质量数据，并进行深入的数据分析和可视化。项目使用Python实现，集成了多个数据源，提供完整的数据处理流水线。

## 功能特性

- **多数据源爬取**: 集成天气网、直气网等多个数据源
- **智能数据处理**: 自动数据清洗、去重、异常值处理
- **全面数据分析**: 基础统计、时间序列分析、相关性分析
- **丰富可视化**: 时间序列图、分布图、热力图等多种图表
- **完整报告生成**: 自动生成HTML格式的分析报告

## 数据指标

### 空气质量指标
- **AQI**: 空气质量指数
- **PM2.5**: 细颗粒物浓度 (μg/m³)
- **PM10**: 可吸入颗粒物浓度 (μg/m³)
- **CO**: 一氧化碳浓度 (mg/m³)
- **NO2**: 二氧化氮浓度 (μg/m³)
- **O3**: 臭氧浓度 (μg/m³)
- **SO2**: 二氧化硫浓度 (μg/m³)

### 气象数据
- **温度**: 最高温度、最低温度 (°C)
- **湿度**: 相对湿度 (%)
- **风向**: 风向信息
- **风力**: 风力等级
- **天气**: 天气状况描述

## 项目结构

```
weather/
├── scrapers/                 # 爬虫模块
│   ├── __init__.py
│   ├── base_scraper.py      # 基础爬虫类
│   ├── tianqi_scraper.py    # 天气网爬虫
│   └── zq12369_scraper.py   # 直气网爬虫
├── data/                    # 数据存储目录
├── output/                  # 分析结果输出目录
├── config.py               # 配置文件
├── data_processor.py       # 数据处理模块
├── analyzer.py             # 数据分析模块
├── main.py                 # 主程序
├── requirements.txt        # 依赖包列表
└── README.md              # 项目说明
```

## 安装与使用

### 1. 环境准备

确保您的系统已安装Python 3.7+

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 运行项目

#### 完整流水线（推荐）
```bash
python main.py
```

#### 分步执行
```bash
# 仅爬取数据
python main.py scrape

# 仅处理数据
python main.py process

# 仅分析数据
python main.py analyze
```

## 输出文件

### 数据文件
- `data/tianqi_raw_data.csv` - 天气网原始数据
- `data/zq12369_raw_data.csv` - 直气网原始数据
- `data/nanjing_air_quality_2024_processed.csv` - 处理后的完整数据

### 分析结果
- `output/monthly_statistics.csv` - 月度统计数据
- `output/seasonal_statistics.csv` - 季节统计数据
- `output/correlation_matrix.csv` - 相关性矩阵
- `output/time_series.html` - 时间序列图表
- `output/monthly_trends.html` - 月度趋势图表
- `output/correlation_heatmap.html` - 相关性热力图
- `output/aqi_distribution.html` - AQI分布分析图

## 技术特点

### 爬虫技术
- 使用requests和BeautifulSoup进行网页解析
- 实现智能重试机制和请求间隔控制
- 支持多种网页结构的自适应解析

### 数据处理
- 自动数据类型转换和格式标准化
- 智能异常值检测和处理
- 多数据源合并和去重
- 缺失值插值和填充

### 数据分析
- 基础统计分析（均值、中位数、标准差等）
- 时间序列分析（月度、季节趋势）
- 相关性分析（污染物间关系）
- 分布分析（AQI等级分布）

### 可视化
- 使用Plotly生成交互式图表
- 支持时间序列、散点图、热力图等多种图表类型
- 自动生成HTML格式的可视化报告

## 配置说明

主要配置项在 `config.py` 中：

- `START_DATE` / `END_DATE`: 数据爬取时间范围
- `SCRAPER_CONFIG`: 爬虫配置（延迟、重试等）
- `HEADERS`: HTTP请求头配置
- `OUTPUT_FILES`: 输出文件路径配置

## 注意事项

1. **网络环境**: 确保网络连接稳定，部分网站可能需要特定的网络环境
2. **爬取频率**: 程序已设置合理的请求间隔，请勿过度频繁爬取
3. **数据完整性**: 由于网站结构可能变化，部分数据可能无法获取
4. **法律合规**: 请确保爬取行为符合相关网站的使用条款

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 尝试使用VPN或代理

2. **数据解析失败**
   - 网站结构可能已更新
   - 检查爬虫代码是否需要调整

3. **依赖包安装失败**
   - 尝试使用国内镜像源
   - 检查Python版本兼容性

## 扩展功能

项目设计具有良好的扩展性，可以：

- 添加新的数据源
- 扩展分析维度
- 增加预测模型
- 集成更多可视化类型

## 许可证

本项目仅用于学习和研究目的，请遵守相关法律法规和网站使用条款。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件联系

---

**免责声明**: 本项目仅用于技术学习和研究，爬取的数据请勿用于商业用途。使用者需自行承担使用风险，并遵守相关法律法规。

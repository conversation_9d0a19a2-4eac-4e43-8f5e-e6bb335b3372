"""
数据分析模块 - 数据分析和可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import logging
from datetime import datetime
from typing import Dict, List, Optional
import config

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


class AirQualityAnalyzer:
    """空气质量数据分析类"""
    
    def __init__(self, data: pd.DataFrame):
        self.data = data.copy()
        self.prepare_data()
    
    def prepare_data(self):
        """准备分析数据"""
        if 'date' in self.data.columns:
            self.data['date'] = pd.to_datetime(self.data['date'])
            self.data = self.data.sort_values('date').reset_index(drop=True)
    
    def basic_statistics(self) -> Dict:
        """基础统计分析"""
        logger.info("进行基础统计分析...")
        
        stats = {}
        
        # 数值字段统计
        numeric_fields = ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2', 'temp_high', 'temp_low']
        
        for field in numeric_fields:
            if field in self.data.columns:
                stats[field] = {
                    'count': self.data[field].count(),
                    'mean': self.data[field].mean(),
                    'median': self.data[field].median(),
                    'std': self.data[field].std(),
                    'min': self.data[field].min(),
                    'max': self.data[field].max(),
                    'q25': self.data[field].quantile(0.25),
                    'q75': self.data[field].quantile(0.75)
                }
        
        # 空气质量等级分布
        if 'aqi_level_calculated' in self.data.columns:
            stats['aqi_level_distribution'] = self.data['aqi_level_calculated'].value_counts().to_dict()
        
        # 季节分布
        if 'season' in self.data.columns:
            stats['seasonal_distribution'] = self.data['season'].value_counts().to_dict()
        
        return stats
    
    def monthly_analysis(self) -> pd.DataFrame:
        """月度分析"""
        logger.info("进行月度分析...")
        
        if 'date' not in self.data.columns:
            return pd.DataFrame()
        
        monthly_data = self.data.copy()
        monthly_data['year_month'] = monthly_data['date'].dt.to_period('M')
        
        # 按月聚合
        numeric_fields = ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2', 'temp_high', 'temp_low']
        agg_dict = {}
        
        for field in numeric_fields:
            if field in monthly_data.columns:
                agg_dict[field] = ['mean', 'median', 'max', 'min', 'std']
        
        monthly_stats = monthly_data.groupby('year_month').agg(agg_dict)
        
        # 扁平化列名
        monthly_stats.columns = ['_'.join(col).strip() for col in monthly_stats.columns.values]
        monthly_stats = monthly_stats.reset_index()
        
        return monthly_stats
    
    def seasonal_analysis(self) -> pd.DataFrame:
        """季节分析"""
        logger.info("进行季节分析...")
        
        if 'season' not in self.data.columns:
            return pd.DataFrame()
        
        numeric_fields = ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2', 'temp_high', 'temp_low']
        agg_dict = {}
        
        for field in numeric_fields:
            if field in self.data.columns:
                agg_dict[field] = ['mean', 'median', 'max', 'min', 'std']
        
        seasonal_stats = self.data.groupby('season').agg(agg_dict)
        seasonal_stats.columns = ['_'.join(col).strip() for col in seasonal_stats.columns.values]
        seasonal_stats = seasonal_stats.reset_index()
        
        return seasonal_stats
    
    def correlation_analysis(self) -> pd.DataFrame:
        """相关性分析"""
        logger.info("进行相关性分析...")
        
        numeric_fields = ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2', 'temp_high', 'temp_low', 'humidity']
        available_fields = [field for field in numeric_fields if field in self.data.columns]
        
        if len(available_fields) < 2:
            return pd.DataFrame()
        
        correlation_matrix = self.data[available_fields].corr()
        return correlation_matrix
    
    def plot_time_series(self, fields: List[str] = None, save_path: str = None):
        """绘制时间序列图"""
        if 'date' not in self.data.columns:
            logger.warning("缺少日期字段，无法绘制时间序列图")
            return
        
        if fields is None:
            fields = ['aqi', 'pm25', 'pm10']
        
        available_fields = [field for field in fields if field in self.data.columns]
        
        if not available_fields:
            logger.warning("没有可用的字段绘制时间序列图")
            return
        
        fig = make_subplots(
            rows=len(available_fields), cols=1,
            subplot_titles=available_fields,
            shared_xaxes=True,
            vertical_spacing=0.05
        )
        
        for i, field in enumerate(available_fields, 1):
            fig.add_trace(
                go.Scatter(
                    x=self.data['date'],
                    y=self.data[field],
                    mode='lines',
                    name=field,
                    line=dict(width=1)
                ),
                row=i, col=1
            )
        
        fig.update_layout(
            title="南京2024年空气质量时间序列",
            height=200 * len(available_fields),
            showlegend=False
        )
        
        fig.update_xaxes(title_text="日期", row=len(available_fields), col=1)
        
        if save_path:
            fig.write_html(save_path)
            logger.info(f"时间序列图已保存到: {save_path}")
        
        return fig
    
    def plot_monthly_trends(self, save_path: str = None):
        """绘制月度趋势图"""
        monthly_data = self.monthly_analysis()
        
        if monthly_data.empty:
            logger.warning("没有月度数据可绘制")
            return
        
        # 选择主要指标
        main_indicators = ['aqi_mean', 'pm25_mean', 'pm10_mean']
        available_indicators = [ind for ind in main_indicators if ind in monthly_data.columns]
        
        if not available_indicators:
            logger.warning("没有可用的月度指标")
            return
        
        fig = go.Figure()
        
        for indicator in available_indicators:
            fig.add_trace(go.Scatter(
                x=monthly_data['year_month'].astype(str),
                y=monthly_data[indicator],
                mode='lines+markers',
                name=indicator.replace('_mean', '').upper(),
                line=dict(width=2)
            ))
        
        fig.update_layout(
            title="南京2024年空气质量月度趋势",
            xaxis_title="月份",
            yaxis_title="浓度值",
            hovermode='x unified'
        )
        
        if save_path:
            fig.write_html(save_path)
            logger.info(f"月度趋势图已保存到: {save_path}")
        
        return fig
    
    def plot_correlation_heatmap(self, save_path: str = None):
        """绘制相关性热力图"""
        corr_matrix = self.correlation_analysis()
        
        if corr_matrix.empty:
            logger.warning("没有相关性数据可绘制")
            return
        
        fig = go.Figure(data=go.Heatmap(
            z=corr_matrix.values,
            x=corr_matrix.columns,
            y=corr_matrix.columns,
            colorscale='RdBu',
            zmid=0,
            text=np.round(corr_matrix.values, 2),
            texttemplate="%{text}",
            textfont={"size": 10},
            hoverongaps=False
        ))
        
        fig.update_layout(
            title="空气质量指标相关性热力图",
            width=600,
            height=600
        )
        
        if save_path:
            fig.write_html(save_path)
            logger.info(f"相关性热力图已保存到: {save_path}")
        
        return fig
    
    def plot_aqi_distribution(self, save_path: str = None):
        """绘制AQI分布图"""
        if 'aqi' not in self.data.columns:
            logger.warning("没有AQI数据可绘制")
            return
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('AQI分布直方图', 'AQI箱线图', 'AQI等级分布', 'AQI季节分布'),
            specs=[[{"type": "histogram"}, {"type": "box"}],
                   [{"type": "pie"}, {"type": "bar"}]]
        )
        
        # 直方图
        fig.add_trace(
            go.Histogram(x=self.data['aqi'], nbinsx=30, name="AQI分布"),
            row=1, col=1
        )
        
        # 箱线图
        fig.add_trace(
            go.Box(y=self.data['aqi'], name="AQI"),
            row=1, col=2
        )
        
        # AQI等级分布饼图
        if 'aqi_level_calculated' in self.data.columns:
            level_counts = self.data['aqi_level_calculated'].value_counts()
            fig.add_trace(
                go.Pie(labels=level_counts.index, values=level_counts.values, name="AQI等级"),
                row=2, col=1
            )
        
        # 季节AQI分布
        if 'season' in self.data.columns:
            seasonal_aqi = self.data.groupby('season')['aqi'].mean()
            fig.add_trace(
                go.Bar(x=seasonal_aqi.index, y=seasonal_aqi.values, name="季节AQI"),
                row=2, col=2
            )
        
        fig.update_layout(
            title="南京2024年AQI分布分析",
            height=800,
            showlegend=False
        )
        
        if save_path:
            fig.write_html(save_path)
            logger.info(f"AQI分布图已保存到: {save_path}")
        
        return fig
    
    def generate_report(self, output_dir: str = None) -> Dict:
        """生成完整的分析报告"""
        logger.info("生成分析报告...")
        
        if output_dir is None:
            output_dir = config.OUTPUT_DIR
        
        # 基础统计
        basic_stats = self.basic_statistics()
        
        # 月度分析
        monthly_stats = self.monthly_analysis()
        
        # 季节分析
        seasonal_stats = self.seasonal_analysis()
        
        # 相关性分析
        correlation_matrix = self.correlation_analysis()
        
        # 生成图表
        charts = {}
        
        # 时间序列图
        time_series_path = f"{output_dir}/time_series.html"
        charts['time_series'] = self.plot_time_series(save_path=time_series_path)
        
        # 月度趋势图
        monthly_trends_path = f"{output_dir}/monthly_trends.html"
        charts['monthly_trends'] = self.plot_monthly_trends(save_path=monthly_trends_path)
        
        # 相关性热力图
        correlation_path = f"{output_dir}/correlation_heatmap.html"
        charts['correlation'] = self.plot_correlation_heatmap(save_path=correlation_path)
        
        # AQI分布图
        aqi_distribution_path = f"{output_dir}/aqi_distribution.html"
        charts['aqi_distribution'] = self.plot_aqi_distribution(save_path=aqi_distribution_path)
        
        # 保存统计数据
        if not monthly_stats.empty:
            monthly_stats.to_csv(f"{output_dir}/monthly_statistics.csv", index=False, encoding='utf-8-sig')
        
        if not seasonal_stats.empty:
            seasonal_stats.to_csv(f"{output_dir}/seasonal_statistics.csv", index=False, encoding='utf-8-sig')
        
        if not correlation_matrix.empty:
            correlation_matrix.to_csv(f"{output_dir}/correlation_matrix.csv", encoding='utf-8-sig')
        
        report = {
            'basic_statistics': basic_stats,
            'monthly_analysis': monthly_stats,
            'seasonal_analysis': seasonal_stats,
            'correlation_analysis': correlation_matrix,
            'charts': charts
        }
        
        logger.info(f"分析报告已生成，文件保存在: {output_dir}")
        return report

"""
配置文件 - 包含爬虫的URL、参数和设置
"""

import os
from datetime import datetime, timedelta

# 基础配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, 'data')
OUTPUT_DIR = os.path.join(BASE_DIR, 'output')

# 创建目录
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 目标城市
CITY = "南京"
CITY_CODE = "nanjing"

# 时间范围 - 2024年全年
START_DATE = datetime(2024, 1, 1)
END_DATE = datetime(2024, 12, 31)

# 天气网配置
TIANQI_BASE_URL = "https://lishi.tianqi.com"
TIANQI_URLS = {
    'monthly': f"{TIANQI_BASE_URL}/{CITY_CODE}/{{year}}{{month:02d}}.html",
    'daily': f"{TIANQI_BASE_URL}/{CITY_CODE}/{{year}}{{month:02d}}{{day:02d}}.html"
}

# 直气网配置
ZQ12369_BASE_URL = "https://www.zq12369.com"
ZQ12369_URLS = {
    'environment': f"{ZQ12369_BASE_URL}/environment.php?city=%E5%8D%97%E4%BA%AC&tab=city"
}

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 爬虫配置
SCRAPER_CONFIG = {
    'delay': 1,  # 请求间隔（秒）
    'timeout': 30,  # 请求超时时间
    'max_retries': 3,  # 最大重试次数
    'retry_delay': 5,  # 重试间隔
}

# 数据字段配置
AIR_QUALITY_FIELDS = [
    'date',
    'aqi',
    'pm25',
    'pm10',
    'co',
    'no2',
    'o3',
    'so2',
    'primary_pollutant',
    'air_quality_level',
    'temp_high',
    'temp_low',
    'humidity',
    'wind_direction',
    'wind_level',
    'weather'
]

# 输出文件配置
OUTPUT_FILES = {
    'raw_data': os.path.join(DATA_DIR, 'nanjing_air_quality_2024_raw.csv'),
    'processed_data': os.path.join(DATA_DIR, 'nanjing_air_quality_2024_processed.csv'),
    'monthly_summary': os.path.join(OUTPUT_DIR, 'monthly_summary.csv'),
    'analysis_report': os.path.join(OUTPUT_DIR, 'analysis_report.html'),
}

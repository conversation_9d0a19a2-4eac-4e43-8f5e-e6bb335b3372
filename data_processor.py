"""
数据处理模块 - 数据清洗、合并和预处理
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import List, Dict, Optional
import config

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理类"""
    
    def __init__(self):
        self.processed_data = None
    
    def merge_data_sources(self, tianqi_data: List[Dict], zq12369_data: List[Dict]) -> pd.DataFrame:
        """
        合并不同数据源的数据
        
        Args:
            tianqi_data: 天气网数据
            zq12369_data: 直气网数据
            
        Returns:
            合并后的DataFrame
        """
        logger.info("开始合并数据源...")
        
        # 转换为DataFrame
        df_tianqi = pd.DataFrame(tianqi_data) if tianqi_data else pd.DataFrame()
        df_zq12369 = pd.DataFrame(zq12369_data) if zq12369_data else pd.DataFrame()
        
        # 确保日期列存在且格式正确
        for df in [df_tianqi, df_zq12369]:
            if not df.empty and 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])
        
        # 合并数据
        if df_tianqi.empty and df_zq12369.empty:
            logger.warning("所有数据源都为空")
            return pd.DataFrame()
        elif df_tianqi.empty:
            merged_df = df_zq12369.copy()
        elif df_zq12369.empty:
            merged_df = df_tianqi.copy()
        else:
            # 基于日期合并
            merged_df = pd.merge(df_tianqi, df_zq12369, on='date', how='outer', suffixes=('_tianqi', '_zq12369'))
            
            # 合并重复字段，优先使用更完整的数据
            self._merge_duplicate_columns(merged_df)
        
        logger.info(f"数据合并完成，共 {len(merged_df)} 条记录")
        return merged_df
    
    def _merge_duplicate_columns(self, df: pd.DataFrame):
        """合并重复的列"""
        duplicate_fields = ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2', 'air_quality_level']
        
        for field in duplicate_fields:
            tianqi_col = f"{field}_tianqi"
            zq12369_col = f"{field}_zq12369"
            
            if tianqi_col in df.columns and zq12369_col in df.columns:
                # 合并策略：优先使用非空值，如果都有值则取平均值（数值型）或优先使用zq12369（字符型）
                if field in ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2']:
                    # 数值型字段取平均值
                    df[field] = df[[tianqi_col, zq12369_col]].mean(axis=1, skipna=True)
                else:
                    # 字符型字段优先使用zq12369
                    df[field] = df[zq12369_col].fillna(df[tianqi_col])
                
                # 删除原始列
                df.drop([tianqi_col, zq12369_col], axis=1, inplace=True)
            elif tianqi_col in df.columns:
                df[field] = df[tianqi_col]
                df.drop(tianqi_col, axis=1, inplace=True)
            elif zq12369_col in df.columns:
                df[field] = df[zq12369_col]
                df.drop(zq12369_col, axis=1, inplace=True)
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据清洗
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        logger.info("开始数据清洗...")
        
        if df.empty:
            logger.warning("输入数据为空")
            return df
        
        cleaned_df = df.copy()
        
        # 1. 处理日期
        if 'date' in cleaned_df.columns:
            cleaned_df['date'] = pd.to_datetime(cleaned_df['date'])
            cleaned_df = cleaned_df.sort_values('date').reset_index(drop=True)
        
        # 2. 处理数值型字段
        numeric_fields = ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2', 'temp_high', 'temp_low', 'humidity']
        
        for field in numeric_fields:
            if field in cleaned_df.columns:
                # 转换为数值型
                cleaned_df[field] = pd.to_numeric(cleaned_df[field], errors='coerce')
                
                # 处理异常值
                cleaned_df = self._handle_outliers(cleaned_df, field)
        
        # 3. 处理缺失值
        cleaned_df = self._handle_missing_values(cleaned_df)
        
        # 4. 数据验证
        cleaned_df = self._validate_data(cleaned_df)
        
        # 5. 添加衍生字段
        cleaned_df = self._add_derived_fields(cleaned_df)
        
        logger.info(f"数据清洗完成，剩余 {len(cleaned_df)} 条有效记录")
        return cleaned_df
    
    def _handle_outliers(self, df: pd.DataFrame, field: str) -> pd.DataFrame:
        """处理异常值"""
        if field not in df.columns or df[field].isna().all():
            return df
        
        # 定义合理范围
        ranges = {
            'aqi': (0, 500),
            'pm25': (0, 500),
            'pm10': (0, 600),
            'co': (0, 50),
            'no2': (0, 200),
            'o3': (0, 400),
            'so2': (0, 200),
            'temp_high': (-30, 50),
            'temp_low': (-40, 40),
            'humidity': (0, 100)
        }
        
        if field in ranges:
            min_val, max_val = ranges[field]
            outlier_mask = (df[field] < min_val) | (df[field] > max_val)
            outlier_count = outlier_mask.sum()
            
            if outlier_count > 0:
                logger.warning(f"{field} 发现 {outlier_count} 个异常值")
                df.loc[outlier_mask, field] = np.nan
        
        return df
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        # 对于数值型字段，使用插值法填充
        numeric_fields = ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2', 'temp_high', 'temp_low', 'humidity']
        
        for field in numeric_fields:
            if field in df.columns:
                # 使用线性插值
                df[field] = df[field].interpolate(method='linear', limit_direction='both')
                
                # 如果仍有缺失值，使用前向填充
                df[field] = df[field].fillna(method='ffill')
                
                # 如果开头有缺失值，使用后向填充
                df[field] = df[field].fillna(method='bfill')
        
        return df
    
    def _validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """数据验证"""
        # 移除完全空白的行
        df = df.dropna(how='all')
        
        # 确保至少有日期和一个空气质量指标
        required_fields = ['date']
        air_quality_fields = ['aqi', 'pm25', 'pm10']
        
        # 保留至少有一个空气质量指标的记录
        mask = df[air_quality_fields].notna().any(axis=1)
        df = df[mask]
        
        return df
    
    def _add_derived_fields(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加衍生字段"""
        if df.empty:
            return df
        
        # 添加年、月、日、星期字段
        if 'date' in df.columns:
            df['year'] = df['date'].dt.year
            df['month'] = df['date'].dt.month
            df['day'] = df['date'].dt.day
            df['weekday'] = df['date'].dt.dayofweek
            df['season'] = df['month'].map(self._get_season)
        
        # 根据AQI计算空气质量等级
        if 'aqi' in df.columns and df['aqi'].notna().any():
            df['aqi_level_calculated'] = df['aqi'].apply(self._calculate_aqi_level)
        
        # 计算温差
        if 'temp_high' in df.columns and 'temp_low' in df.columns:
            df['temp_diff'] = df['temp_high'] - df['temp_low']
        
        return df
    
    def _get_season(self, month: int) -> str:
        """根据月份获取季节"""
        if month in [12, 1, 2]:
            return '冬季'
        elif month in [3, 4, 5]:
            return '春季'
        elif month in [6, 7, 8]:
            return '夏季'
        else:
            return '秋季'
    
    def _calculate_aqi_level(self, aqi: float) -> str:
        """根据AQI值计算空气质量等级"""
        if pd.isna(aqi):
            return '未知'
        elif aqi <= 50:
            return '优'
        elif aqi <= 100:
            return '良'
        elif aqi <= 150:
            return '轻度污染'
        elif aqi <= 200:
            return '中度污染'
        elif aqi <= 300:
            return '重度污染'
        else:
            return '严重污染'
    
    def process_data(self, tianqi_data: List[Dict], zq12369_data: List[Dict]) -> pd.DataFrame:
        """
        完整的数据处理流程
        
        Args:
            tianqi_data: 天气网数据
            zq12369_data: 直气网数据
            
        Returns:
            处理后的DataFrame
        """
        # 合并数据源
        merged_df = self.merge_data_sources(tianqi_data, zq12369_data)
        
        # 清洗数据
        cleaned_df = self.clean_data(merged_df)
        
        # 保存处理后的数据
        self.processed_data = cleaned_df
        
        return cleaned_df
    
    def save_processed_data(self, filename: str = None):
        """保存处理后的数据"""
        if self.processed_data is None:
            logger.warning("没有处理后的数据可保存")
            return
        
        if filename is None:
            filename = config.OUTPUT_FILES['processed_data']
        
        self.processed_data.to_csv(filename, index=False, encoding='utf-8-sig')
        logger.info(f"处理后的数据已保存到: {filename}")
    
    def get_data_summary(self) -> Dict:
        """获取数据摘要"""
        if self.processed_data is None:
            return {}
        
        df = self.processed_data
        
        summary = {
            'total_records': len(df),
            'date_range': {
                'start': df['date'].min().strftime('%Y-%m-%d') if 'date' in df.columns else None,
                'end': df['date'].max().strftime('%Y-%m-%d') if 'date' in df.columns else None
            },
            'missing_data': df.isnull().sum().to_dict(),
            'data_completeness': (1 - df.isnull().sum() / len(df)).to_dict()
        }
        
        # 数值字段的统计信息
        numeric_fields = ['aqi', 'pm25', 'pm10', 'co', 'no2', 'o3', 'so2']
        for field in numeric_fields:
            if field in df.columns:
                summary[f'{field}_stats'] = {
                    'mean': df[field].mean(),
                    'median': df[field].median(),
                    'min': df[field].min(),
                    'max': df[field].max(),
                    'std': df[field].std()
                }
        
        return summary

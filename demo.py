"""
项目演示脚本 - 使用模拟数据展示项目功能
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from data_processor import DataProcessor
from analyzer import AirQualityAnalyzer

def generate_demo_data():
    """生成演示数据"""
    print("生成演示数据...")
    
    # 生成2024年1月的模拟数据
    dates = pd.date_range('2024-01-01', '2024-01-31', freq='D')
    
    # 模拟天气网数据
    tianqi_data = []
    for i, date in enumerate(dates):
        # 模拟冬季南京的空气质量数据
        base_aqi = 80 + np.random.normal(0, 20)
        base_aqi = max(30, min(200, base_aqi))  # 限制在合理范围内
        
        weather_conditions = ['晴', '多云', '阴', '小雨', '雾', '霾']
        weather = np.random.choice(weather_conditions)
        
        tianqi_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'aqi': round(base_aqi),
            'weather': weather,
            'temp_high': round(np.random.normal(8, 5)),  # 冬季温度
            'temp_low': round(np.random.normal(-2, 4)),
            'wind_direction': np.random.choice(['北风', '南风', '东风', '西风', '东北风', '西北风']),
            'wind_level': np.random.choice(['1-2级', '3-4级', '5-6级']),
            'humidity': round(np.random.normal(65, 15))
        })
    
    # 模拟直气网数据
    zq12369_data = []
    for i, date in enumerate(dates):
        # 基于AQI生成相关的污染物数据
        aqi = tianqi_data[i]['aqi']
        
        # PM2.5通常与AQI相关
        pm25 = max(10, aqi * 0.7 + np.random.normal(0, 10))
        pm10 = max(15, pm25 * 1.4 + np.random.normal(0, 15))
        
        zq12369_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'aqi': round(aqi + np.random.normal(0, 5)),  # 略有差异
            'pm25': round(pm25),
            'pm10': round(pm10),
            'co': round(np.random.normal(1.2, 0.3), 1),
            'no2': round(np.random.normal(45, 15)),
            'o3': round(np.random.normal(60, 20)),
            'so2': round(np.random.normal(15, 8)),
            'primary_pollutant': 'PM2.5' if pm25 > 75 else 'PM10' if pm10 > 150 else '无'
        })
    
    print(f"✓ 生成了 {len(tianqi_data)} 条天气网数据")
    print(f"✓ 生成了 {len(zq12369_data)} 条直气网数据")
    
    return tianqi_data, zq12369_data

def demo_data_processing():
    """演示数据处理功能"""
    print("\n" + "="*50)
    print("数据处理演示")
    print("="*50)
    
    # 生成演示数据
    tianqi_data, zq12369_data = generate_demo_data()
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 处理数据
    processed_data = processor.process_data(tianqi_data, zq12369_data)
    
    print(f"\n处理后数据概览:")
    print(f"- 记录数: {len(processed_data)}")
    print(f"- 字段数: {len(processed_data.columns)}")
    print(f"- 时间范围: {processed_data['date'].min()} 到 {processed_data['date'].max()}")
    
    print(f"\n主要字段:")
    for col in ['aqi', 'pm25', 'pm10', 'temp_high', 'temp_low']:
        if col in processed_data.columns:
            mean_val = processed_data[col].mean()
            print(f"- {col}: 平均值 {mean_val:.1f}")
    
    # 保存演示数据
    demo_file = os.path.join(config.DATA_DIR, 'demo_data.csv')
    processed_data.to_csv(demo_file, index=False, encoding='utf-8-sig')
    print(f"\n✓ 演示数据已保存到: {demo_file}")
    
    return processed_data

def demo_analysis():
    """演示数据分析功能"""
    print("\n" + "="*50)
    print("数据分析演示")
    print("="*50)
    
    # 处理数据
    processed_data = demo_data_processing()
    
    # 创建分析器
    analyzer = AirQualityAnalyzer(processed_data)
    
    # 基础统计分析
    print("\n1. 基础统计分析:")
    stats = analyzer.basic_statistics()
    
    for field in ['aqi', 'pm25', 'pm10']:
        if field in stats:
            field_stats = stats[field]
            print(f"\n{field.upper()}:")
            print(f"  平均值: {field_stats['mean']:.1f}")
            print(f"  中位数: {field_stats['median']:.1f}")
            print(f"  最大值: {field_stats['max']:.1f}")
            print(f"  最小值: {field_stats['min']:.1f}")
            print(f"  标准差: {field_stats['std']:.1f}")
    
    # 空气质量等级分布
    if 'aqi_level_distribution' in stats:
        print(f"\n2. 空气质量等级分布:")
        for level, count in stats['aqi_level_distribution'].items():
            percentage = (count / len(processed_data)) * 100
            print(f"  {level}: {count} 天 ({percentage:.1f}%)")
    
    # 相关性分析
    print(f"\n3. 相关性分析:")
    corr_matrix = analyzer.correlation_analysis()
    if not corr_matrix.empty:
        # 显示AQI与其他指标的相关性
        if 'aqi' in corr_matrix.columns:
            aqi_corr = corr_matrix['aqi'].sort_values(ascending=False)
            print(f"  AQI与其他指标的相关性:")
            for field, corr_val in aqi_corr.items():
                if field != 'aqi' and not pd.isna(corr_val):
                    print(f"    {field}: {corr_val:.3f}")
    
    # 生成图表（保存为HTML文件）
    print(f"\n4. 生成可视化图表:")
    try:
        # 时间序列图
        time_series_fig = analyzer.plot_time_series(
            fields=['aqi', 'pm25', 'pm10'],
            save_path=os.path.join(config.OUTPUT_DIR, 'demo_time_series.html')
        )
        print(f"  ✓ 时间序列图已保存")
        
        # 相关性热力图
        correlation_fig = analyzer.plot_correlation_heatmap(
            save_path=os.path.join(config.OUTPUT_DIR, 'demo_correlation.html')
        )
        print(f"  ✓ 相关性热力图已保存")
        
        # AQI分布图
        aqi_dist_fig = analyzer.plot_aqi_distribution(
            save_path=os.path.join(config.OUTPUT_DIR, 'demo_aqi_distribution.html')
        )
        print(f"  ✓ AQI分布图已保存")
        
    except Exception as e:
        print(f"  ⚠️ 图表生成遇到问题: {e}")
    
    print(f"\n✓ 分析完成！结果文件保存在: {config.OUTPUT_DIR}")

def demo_full_workflow():
    """演示完整工作流程"""
    print("="*60)
    print("南京2024年空气质量数据项目 - 功能演示")
    print("="*60)
    print("注意：此演示使用模拟数据，展示项目的完整功能流程")
    print("实际使用时，数据将从真实网站爬取")
    
    try:
        # 演示数据分析
        demo_analysis()
        
        print("\n" + "="*60)
        print("演示完成！")
        print("="*60)
        print("\n生成的文件:")
        
        # 列出生成的文件
        data_files = []
        output_files = []
        
        if os.path.exists(config.DATA_DIR):
            data_files = [f for f in os.listdir(config.DATA_DIR) if f.endswith('.csv')]
        
        if os.path.exists(config.OUTPUT_DIR):
            output_files = [f for f in os.listdir(config.OUTPUT_DIR) if f.endswith('.html')]
        
        if data_files:
            print(f"\n数据文件 ({config.DATA_DIR}):")
            for file in data_files:
                print(f"  - {file}")
        
        if output_files:
            print(f"\n分析结果 ({config.OUTPUT_DIR}):")
            for file in output_files:
                print(f"  - {file}")
        
        print(f"\n要运行真实的数据爬取，请执行:")
        print(f"  python main.py")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    demo_full_workflow()

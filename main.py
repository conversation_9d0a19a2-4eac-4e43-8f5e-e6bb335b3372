"""
主程序 - 南京2024年空气质量数据爬取与分析
"""

import logging
import sys
import os
from datetime import datetime
import pandas as pd
from tqdm import tqdm

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
from scrapers import TianqiScraper, ZQ12369Scraper
from data_processor import DataProcessor
from analyzer import AirQualityAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('air_quality_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class AirQualityProject:
    """南京空气质量数据项目主类"""
    
    def __init__(self):
        self.tianqi_scraper = TianqiScraper()
        self.zq12369_scraper = ZQ12369Scraper()
        self.data_processor = DataProcessor()
        self.analyzer = None
        
        self.tianqi_data = []
        self.zq12369_data = []
        self.processed_data = None
    
    def scrape_all_data(self):
        """爬取所有数据源的数据"""
        logger.info("开始爬取南京2024年空气质量数据...")
        logger.info(f"时间范围: {config.START_DATE.strftime('%Y-%m-%d')} 到 {config.END_DATE.strftime('%Y-%m-%d')}")
        
        # 爬取天气网数据
        logger.info("正在爬取天气网数据...")
        try:
            self.tianqi_data = self.tianqi_scraper.scrape_data(config.START_DATE, config.END_DATE)
            logger.info(f"天气网数据爬取完成: {len(self.tianqi_data)} 条记录")
            
            # 保存原始数据
            if self.tianqi_data:
                tianqi_file = os.path.join(config.DATA_DIR, 'tianqi_raw_data.csv')
                self.tianqi_scraper.save_data(self.tianqi_data, tianqi_file)
                
        except Exception as e:
            logger.error(f"天气网数据爬取失败: {e}")
            self.tianqi_data = []
        
        # 爬取直气网数据
        logger.info("正在爬取直气网数据...")
        try:
            self.zq12369_data = self.zq12369_scraper.scrape_data(config.START_DATE, config.END_DATE)
            logger.info(f"直气网数据爬取完成: {len(self.zq12369_data)} 条记录")
            
            # 保存原始数据
            if self.zq12369_data:
                zq12369_file = os.path.join(config.DATA_DIR, 'zq12369_raw_data.csv')
                self.zq12369_scraper.save_data(self.zq12369_data, zq12369_file)
                
        except Exception as e:
            logger.error(f"直气网数据爬取失败: {e}")
            self.zq12369_data = []
        
        # 检查是否有数据
        total_records = len(self.tianqi_data) + len(self.zq12369_data)
        if total_records == 0:
            logger.error("所有数据源都没有获取到数据！")
            return False
        
        logger.info(f"数据爬取完成，总计 {total_records} 条原始记录")
        return True
    
    def process_data(self):
        """处理和清洗数据"""
        logger.info("开始数据处理...")
        
        try:
            # 处理数据
            self.processed_data = self.data_processor.process_data(self.tianqi_data, self.zq12369_data)
            
            if self.processed_data.empty:
                logger.error("数据处理后为空！")
                return False
            
            # 保存处理后的数据
            self.data_processor.save_processed_data()
            
            # 打印数据摘要
            summary = self.data_processor.get_data_summary()
            logger.info("数据处理完成")
            logger.info(f"处理后数据记录数: {summary.get('total_records', 0)}")
            logger.info(f"数据时间范围: {summary.get('date_range', {}).get('start')} 到 {summary.get('date_range', {}).get('end')}")
            
            return True
            
        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            return False
    
    def analyze_data(self):
        """分析数据"""
        if self.processed_data is None or self.processed_data.empty:
            logger.error("没有处理后的数据可分析")
            return False
        
        logger.info("开始数据分析...")
        
        try:
            # 创建分析器
            self.analyzer = AirQualityAnalyzer(self.processed_data)
            
            # 生成分析报告
            report = self.analyzer.generate_report()
            
            # 打印基础统计信息
            basic_stats = report.get('basic_statistics', {})
            if 'aqi' in basic_stats:
                aqi_stats = basic_stats['aqi']
                logger.info(f"AQI统计 - 平均值: {aqi_stats.get('mean', 0):.1f}, "
                           f"中位数: {aqi_stats.get('median', 0):.1f}, "
                           f"最大值: {aqi_stats.get('max', 0):.1f}")
            
            if 'aqi_level_distribution' in basic_stats:
                level_dist = basic_stats['aqi_level_distribution']
                logger.info("空气质量等级分布:")
                for level, count in level_dist.items():
                    logger.info(f"  {level}: {count} 天")
            
            logger.info("数据分析完成，报告已生成")
            return True
            
        except Exception as e:
            logger.error(f"数据分析失败: {e}")
            return False
    
    def run_full_pipeline(self):
        """运行完整的数据处理流水线"""
        logger.info("=" * 60)
        logger.info("南京2024年空气质量数据爬取与分析项目启动")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # 步骤1: 爬取数据
        if not self.scrape_all_data():
            logger.error("数据爬取失败，程序终止")
            return False
        
        # 步骤2: 处理数据
        if not self.process_data():
            logger.error("数据处理失败，程序终止")
            return False
        
        # 步骤3: 分析数据
        if not self.analyze_data():
            logger.error("数据分析失败，程序终止")
            return False
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("项目执行完成！")
        logger.info(f"总耗时: {duration}")
        logger.info(f"数据文件保存在: {config.DATA_DIR}")
        logger.info(f"分析结果保存在: {config.OUTPUT_DIR}")
        logger.info("=" * 60)
        
        return True
    
    def load_existing_data(self):
        """加载已存在的数据（用于重新分析）"""
        processed_file = config.OUTPUT_FILES['processed_data']
        
        if os.path.exists(processed_file):
            logger.info(f"加载已存在的处理数据: {processed_file}")
            self.processed_data = pd.read_csv(processed_file)
            self.processed_data['date'] = pd.to_datetime(self.processed_data['date'])
            logger.info(f"加载数据完成: {len(self.processed_data)} 条记录")
            return True
        else:
            logger.warning("没有找到已处理的数据文件")
            return False


def main():
    """主函数"""
    project = AirQualityProject()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'analyze':
            # 仅进行分析（使用已有数据）
            if project.load_existing_data():
                project.analyze_data()
            else:
                logger.error("没有找到已处理的数据，请先运行完整流水线")
        elif command == 'scrape':
            # 仅爬取数据
            project.scrape_all_data()
        elif command == 'process':
            # 仅处理数据
            if project.load_existing_data():
                project.process_data()
            else:
                logger.error("没有找到原始数据，请先爬取数据")
        else:
            logger.error(f"未知命令: {command}")
            logger.info("可用命令: scrape, process, analyze")
    else:
        # 运行完整流水线
        project.run_full_pipeline()


if __name__ == "__main__":
    main()

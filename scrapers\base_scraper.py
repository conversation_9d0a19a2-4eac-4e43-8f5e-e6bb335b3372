"""
基础爬虫类 - 提供通用的爬虫功能
"""

import requests
import time
import logging
from abc import ABC, abstractmethod
from bs4 import BeautifulSoup
from typing import Dict, List, Optional
import config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BaseScraper(ABC):
    """基础爬虫类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(config.HEADERS)
        self.delay = config.SCRAPER_CONFIG['delay']
        self.timeout = config.SCRAPER_CONFIG['timeout']
        self.max_retries = config.SCRAPER_CONFIG['max_retries']
        self.retry_delay = config.SCRAPER_CONFIG['retry_delay']
    
    def get_page(self, url: str, params: Optional[Dict] = None) -> Optional[BeautifulSoup]:
        """
        获取网页内容
        
        Args:
            url: 目标URL
            params: 请求参数
            
        Returns:
            BeautifulSoup对象或None
        """
        for attempt in range(self.max_retries):
            try:
                logger.info(f"正在请求: {url} (尝试 {attempt + 1}/{self.max_retries})")
                
                response = self.session.get(
                    url, 
                    params=params, 
                    timeout=self.timeout
                )
                response.raise_for_status()
                response.encoding = 'utf-8'
                
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 请求间隔
                time.sleep(self.delay)
                
                return soup
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"请求最终失败: {url}")
                    return None
    
    def clean_text(self, text: str) -> str:
        """清理文本数据"""
        if not text:
            return ""
        return text.strip().replace('\n', '').replace('\r', '').replace('\t', '')
    
    def extract_number(self, text: str) -> Optional[float]:
        """从文本中提取数字"""
        if not text:
            return None
        
        import re
        # 匹配数字（包括小数）
        match = re.search(r'(\d+\.?\d*)', text.replace(',', ''))
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                return None
        return None
    
    @abstractmethod
    def scrape_data(self, start_date, end_date) -> List[Dict]:
        """
        抽象方法：爬取数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            数据列表
        """
        pass
    
    def save_data(self, data: List[Dict], filename: str):
        """保存数据到文件"""
        import pandas as pd
        
        if not data:
            logger.warning("没有数据可保存")
            return
        
        df = pd.DataFrame(data)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        logger.info(f"数据已保存到: {filename}")
    
    def __del__(self):
        """析构函数：关闭session"""
        if hasattr(self, 'session'):
            self.session.close()

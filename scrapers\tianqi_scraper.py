"""
天气网爬虫 - 爬取历史天气和空气质量数据
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List
from .base_scraper import BaseScraper
import config

logger = logging.getLogger(__name__)


class TianqiScraper(BaseScraper):
    """天气网爬虫类"""
    
    def __init__(self):
        super().__init__()
        self.base_url = config.TIANQI_BASE_URL
    
    def scrape_monthly_data(self, year: int, month: int) -> List[Dict]:
        """
        爬取指定月份的数据
        
        Args:
            year: 年份
            month: 月份
            
        Returns:
            该月的数据列表
        """
        url = config.TIANQI_URLS['monthly'].format(year=year, month=month)
        soup = self.get_page(url)
        
        if not soup:
            logger.error(f"无法获取 {year}年{month}月 的数据")
            return []
        
        data = []
        
        try:
            # 查找数据表格
            table = soup.find('table', class_='b')
            if not table:
                logger.warning(f"未找到数据表格: {year}年{month}月")
                return []
            
            rows = table.find_all('tr')[1:]  # 跳过表头
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 8:
                    try:
                        # 解析日期
                        date_text = self.clean_text(cells[0].get_text())
                        date_obj = datetime.strptime(f"{year}-{month:02d}-{date_text}", "%Y-%m-%d")
                        
                        # 解析天气数据
                        weather_data = {
                            'date': date_obj.strftime('%Y-%m-%d'),
                            'weather': self.clean_text(cells[1].get_text()),
                            'temp_high': self.extract_number(cells[2].get_text()),
                            'temp_low': self.extract_number(cells[3].get_text()),
                            'wind_direction': self.clean_text(cells[4].get_text()),
                            'wind_level': self.clean_text(cells[5].get_text()),
                        }
                        
                        # 尝试获取空气质量数据
                        if len(cells) > 6:
                            aqi_text = self.clean_text(cells[6].get_text())
                            weather_data['aqi'] = self.extract_number(aqi_text)
                        
                        if len(cells) > 7:
                            quality_text = self.clean_text(cells[7].get_text())
                            weather_data['air_quality_level'] = quality_text
                        
                        data.append(weather_data)
                        
                    except Exception as e:
                        logger.warning(f"解析行数据失败: {e}")
                        continue
            
            logger.info(f"成功爬取 {year}年{month}月 数据: {len(data)} 条记录")
            
        except Exception as e:
            logger.error(f"解析 {year}年{month}月 数据失败: {e}")
        
        return data
    
    def scrape_data(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """
        爬取指定时间范围的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            数据列表
        """
        all_data = []
        current_date = start_date.replace(day=1)  # 从月初开始
        
        while current_date <= end_date:
            monthly_data = self.scrape_monthly_data(current_date.year, current_date.month)
            all_data.extend(monthly_data)
            
            # 移动到下个月
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)
        
        # 过滤日期范围
        filtered_data = []
        for item in all_data:
            item_date = datetime.strptime(item['date'], '%Y-%m-%d')
            if start_date <= item_date <= end_date:
                filtered_data.append(item)
        
        logger.info(f"天气网总共爬取数据: {len(filtered_data)} 条记录")
        return filtered_data
    
    def get_detailed_air_quality(self, date: datetime) -> Dict:
        """
        获取指定日期的详细空气质量数据
        
        Args:
            date: 目标日期
            
        Returns:
            详细空气质量数据
        """
        # 构建详细页面URL（如果存在）
        url = config.TIANQI_URLS['daily'].format(
            year=date.year, 
            month=date.month, 
            day=date.day
        )
        
        soup = self.get_page(url)
        if not soup:
            return {}
        
        air_quality_data = {}
        
        try:
            # 查找空气质量详细信息
            # 这里需要根据实际网页结构来解析
            # 由于网页结构可能变化，这里提供一个基础框架
            
            # 查找AQI相关信息
            aqi_elements = soup.find_all(text=lambda text: text and 'AQI' in text)
            for element in aqi_elements:
                parent = element.parent
                if parent:
                    aqi_value = self.extract_number(parent.get_text())
                    if aqi_value:
                        air_quality_data['aqi'] = aqi_value
                        break
            
            # 查找PM2.5等污染物数据
            pollutants = ['PM2.5', 'PM10', 'CO', 'NO2', 'O3', 'SO2']
            for pollutant in pollutants:
                elements = soup.find_all(text=lambda text: text and pollutant in text)
                for element in elements:
                    parent = element.parent
                    if parent:
                        value = self.extract_number(parent.get_text())
                        if value:
                            air_quality_data[pollutant.lower().replace('.', '')] = value
                            break
            
        except Exception as e:
            logger.warning(f"获取详细空气质量数据失败: {e}")
        
        return air_quality_data

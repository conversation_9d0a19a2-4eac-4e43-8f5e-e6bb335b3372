"""
直气网爬虫 - 爬取实时和历史空气质量数据
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List
from .base_scraper import BaseScraper
import config

logger = logging.getLogger(__name__)


class ZQ12369Scraper(BaseScraper):
    """直气网爬虫类"""
    
    def __init__(self):
        super().__init__()
        self.base_url = config.ZQ12369_BASE_URL
    
    def scrape_current_data(self) -> Dict:
        """
        爬取当前实时空气质量数据
        
        Returns:
            当前空气质量数据
        """
        url = config.ZQ12369_URLS['environment']
        soup = self.get_page(url)
        
        if not soup:
            logger.error("无法获取实时空气质量数据")
            return {}
        
        current_data = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        try:
            # 查找AQI数据
            aqi_element = soup.find('div', class_='aqi-num') or soup.find('span', class_='aqi')
            if aqi_element:
                aqi_value = self.extract_number(aqi_element.get_text())
                if aqi_value:
                    current_data['aqi'] = aqi_value
            
            # 查找空气质量等级
            quality_element = soup.find('div', class_='aqi-level') or soup.find('span', class_='level')
            if quality_element:
                current_data['air_quality_level'] = self.clean_text(quality_element.get_text())
            
            # 查找主要污染物
            pollutant_element = soup.find('div', class_='primary-pollutant')
            if pollutant_element:
                current_data['primary_pollutant'] = self.clean_text(pollutant_element.get_text())
            
            # 查找各项污染物数据
            pollutants_map = {
                'PM2.5': 'pm25',
                'PM10': 'pm10',
                'CO': 'co',
                'NO2': 'no2',
                'O3': 'o3',
                'SO2': 'so2'
            }
            
            for pollutant_name, field_name in pollutants_map.items():
                # 尝试多种选择器来查找污染物数据
                selectors = [
                    f'[data-pollutant="{pollutant_name}"]',
                    f'.pollutant-{pollutant_name.lower()}',
                    f'#{pollutant_name.lower()}',
                ]
                
                for selector in selectors:
                    element = soup.select_one(selector)
                    if element:
                        value = self.extract_number(element.get_text())
                        if value:
                            current_data[field_name] = value
                            break
                
                # 如果上述方法没找到，尝试文本搜索
                if field_name not in current_data:
                    text_elements = soup.find_all(text=lambda text: text and pollutant_name in text)
                    for text_element in text_elements:
                        parent = text_element.parent
                        if parent:
                            # 查找同级或父级元素中的数值
                            siblings = parent.find_next_siblings()
                            for sibling in siblings:
                                value = self.extract_number(sibling.get_text())
                                if value:
                                    current_data[field_name] = value
                                    break
                            if field_name in current_data:
                                break
            
            logger.info(f"成功获取实时数据: AQI={current_data.get('aqi', 'N/A')}")
            
        except Exception as e:
            logger.error(f"解析实时数据失败: {e}")
        
        return current_data
    
    def scrape_historical_data(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """
        尝试爬取历史数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            历史数据列表
        """
        historical_data = []
        
        # 由于直气网可能不提供完整的历史数据API，
        # 这里我们尝试通过不同的方法获取历史数据
        
        try:
            # 方法1: 尝试查找历史数据页面
            history_url = f"{self.base_url}/history.php?city=南京"
            soup = self.get_page(history_url)
            
            if soup:
                # 解析历史数据表格
                tables = soup.find_all('table')
                for table in tables:
                    rows = table.find_all('tr')[1:]  # 跳过表头
                    
                    for row in rows:
                        cells = row.find_all('td')
                        if len(cells) >= 3:
                            try:
                                date_text = self.clean_text(cells[0].get_text())
                                # 尝试解析日期
                                date_obj = self.parse_date(date_text)
                                
                                if date_obj and start_date <= date_obj <= end_date:
                                    data_item = {
                                        'date': date_obj.strftime('%Y-%m-%d'),
                                        'aqi': self.extract_number(cells[1].get_text()),
                                        'air_quality_level': self.clean_text(cells[2].get_text())
                                    }
                                    
                                    # 尝试获取更多污染物数据
                                    if len(cells) > 3:
                                        pollutant_values = [self.extract_number(cell.get_text()) for cell in cells[3:]]
                                        pollutant_fields = ['pm25', 'pm10', 'co', 'no2', 'o3', 'so2']
                                        
                                        for i, value in enumerate(pollutant_values):
                                            if i < len(pollutant_fields) and value:
                                                data_item[pollutant_fields[i]] = value
                                    
                                    historical_data.append(data_item)
                                    
                            except Exception as e:
                                logger.warning(f"解析历史数据行失败: {e}")
                                continue
            
            # 方法2: 如果有API接口，尝试调用
            # 这里可以添加API调用逻辑
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
        
        logger.info(f"直气网获取历史数据: {len(historical_data)} 条记录")
        return historical_data
    
    def parse_date(self, date_text: str) -> datetime:
        """
        解析日期文本
        
        Args:
            date_text: 日期文本
            
        Returns:
            datetime对象
        """
        date_formats = [
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%m-%d',
            '%m/%d',
            '%Y年%m月%d日',
            '%m月%d日'
        ]
        
        for fmt in date_formats:
            try:
                if '%Y' not in fmt:
                    # 如果没有年份，假设是当前年份
                    date_obj = datetime.strptime(date_text, fmt)
                    date_obj = date_obj.replace(year=datetime.now().year)
                else:
                    date_obj = datetime.strptime(date_text, fmt)
                return date_obj
            except ValueError:
                continue
        
        return None
    
    def scrape_data(self, start_date: datetime, end_date: datetime) -> List[Dict]:
        """
        爬取指定时间范围的数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            数据列表
        """
        all_data = []
        
        # 获取历史数据
        historical_data = self.scrape_historical_data(start_date, end_date)
        all_data.extend(historical_data)
        
        # 如果结束日期是今天，也获取实时数据
        if end_date.date() >= datetime.now().date():
            current_data = self.scrape_current_data()
            if current_data:
                all_data.append(current_data)
        
        return all_data

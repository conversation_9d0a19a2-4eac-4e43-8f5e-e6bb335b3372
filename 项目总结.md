# 南京2024年空气质量数据爬取与分析项目

## 项目概述

本项目是一个完整的空气质量数据爬取、处理和分析系统，专门针对南京市2024年一整年的空气质量数据。项目采用Python开发，集成了多个数据源，提供了从数据爬取到可视化分析的完整解决方案。

## 项目特点

### ✅ 已完成功能

1. **多数据源爬虫系统**
   - 天气网历史数据爬虫
   - 直气网实时数据爬虫
   - 智能重试机制和错误处理
   - 可配置的请求间隔和超时设置

2. **智能数据处理**
   - 多数据源自动合并
   - 数据清洗和异常值处理
   - 缺失值智能填充
   - 数据类型自动转换

3. **全面数据分析**
   - 基础统计分析（均值、中位数、标准差等）
   - 时间序列分析（月度、季节趋势）
   - 相关性分析（污染物间关系）
   - 空气质量等级分布分析

4. **丰富可视化功能**
   - 交互式时间序列图表
   - 相关性热力图
   - AQI分布分析图
   - 月度趋势图表
   - 所有图表支持HTML格式导出

5. **完整项目架构**
   - 模块化设计，易于扩展
   - 配置文件集中管理
   - 完善的日志系统
   - 错误处理和异常恢复

## 技术架构

```
weather/
├── scrapers/              # 爬虫模块
│   ├── base_scraper.py   # 基础爬虫类
│   ├── tianqi_scraper.py # 天气网爬虫
│   └── zq12369_scraper.py# 直气网爬虫
├── data_processor.py     # 数据处理模块
├── analyzer.py           # 数据分析模块
├── config.py            # 配置管理
├── main.py              # 主程序入口
├── demo.py              # 功能演示脚本
├── test_project.py      # 项目测试脚本
└── README.md            # 项目文档
```

## 数据指标

### 空气质量指标
- **AQI**: 空气质量指数
- **PM2.5**: 细颗粒物浓度 (μg/m³)
- **PM10**: 可吸入颗粒物浓度 (μg/m³)
- **CO**: 一氧化碳浓度 (mg/m³)
- **NO2**: 二氧化氮浓度 (μg/m³)
- **O3**: 臭氧浓度 (μg/m³)
- **SO2**: 二氧化硫浓度 (μg/m³)

### 气象数据
- **温度**: 最高温度、最低温度 (°C)
- **湿度**: 相对湿度 (%)
- **风向**: 风向信息
- **风力**: 风力等级
- **天气**: 天气状况描述

## 使用方法

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 功能测试
```bash
# 运行项目测试
python test_project.py
```

### 3. 功能演示
```bash
# 运行演示（使用模拟数据）
python demo.py
```

### 4. 实际数据爬取
```bash
# 完整流水线
python main.py

# 分步执行
python main.py scrape   # 仅爬取数据
python main.py process  # 仅处理数据
python main.py analyze  # 仅分析数据
```

## 项目测试结果

✅ **所有功能测试通过**
- 模块导入测试：通过
- 配置测试：通过
- 爬虫功能测试：通过
- 数据处理测试：通过
- 数据分析测试：通过

## 演示结果

使用模拟的2024年1月南京空气质量数据进行演示：

### 数据概览
- **记录数**: 31天完整数据
- **字段数**: 22个数据字段
- **时间范围**: 2024-01-01 到 2024-01-31

### 主要统计结果
- **AQI平均值**: 73.9（良好水平）
- **PM2.5平均值**: 50.8 μg/m³
- **PM10平均值**: 73.4 μg/m³
- **空气质量分布**: 77.4%为良好，12.9%为优秀

### 生成文件
- **数据文件**: demo_data.csv
- **可视化图表**: 
  - 时间序列图 (demo_time_series.html)
  - 相关性热力图 (demo_correlation.html)
  - AQI分布图 (demo_aqi_distribution.html)

## 技术亮点

1. **智能爬虫设计**
   - 自适应网页结构解析
   - 多种数据提取策略
   - 完善的错误处理机制

2. **高质量数据处理**
   - 多数据源智能合并
   - 异常值自动检测和处理
   - 缺失值多种填充策略

3. **专业数据分析**
   - 统计学方法应用
   - 时间序列分析
   - 多维度相关性分析

4. **现代化可视化**
   - 使用Plotly生成交互式图表
   - 支持缩放、平移等交互操作
   - HTML格式便于分享和展示

## 扩展性

项目具有良好的扩展性，可以轻松：
- 添加新的数据源
- 扩展分析维度
- 增加预测模型
- 集成更多可视化类型
- 支持其他城市数据

## 注意事项

1. **网络环境**: 确保网络连接稳定
2. **爬取频率**: 已设置合理的请求间隔
3. **数据完整性**: 网站结构变化可能影响数据获取
4. **法律合规**: 请遵守相关网站的使用条款

## 项目价值

1. **学术研究**: 为环境科学研究提供数据支持
2. **政策制定**: 为环保政策提供数据依据
3. **公众服务**: 为公众提供空气质量信息
4. **技术示范**: 展示现代数据科学技术应用

## 总结

本项目成功实现了南京2024年空气质量数据的完整处理流水线，从数据爬取到深度分析，再到可视化展示，形成了一个完整的数据科学解决方案。项目代码结构清晰，功能完善，具有很好的实用价值和扩展性。

通过本项目，可以深入了解南京市的空气质量状况，为环境保护和公众健康提供有价值的数据支持。
